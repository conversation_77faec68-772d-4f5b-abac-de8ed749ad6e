package com.hwb.timecontroller.utils

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import androidx.core.net.toUri
import com.hjq.toast.Toaster
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 通过无障碍服务自动获取悬浮窗权限的工具类
 */
class AccessibilityOverlayPermissionHelper(
    private val accessibilityService: AccessibilityService
) {

    companion object {
        private const val TAG = "AccessibilityOverlayHelper"
        private const val SETTINGS_PACKAGE = "com.android.settings"
        private const val MAX_RETRY_COUNT = 10
        private const val RETRY_DELAY_MS = 1000L
    }

    private val handler = Handler(Looper.getMainLooper())
    private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
    private var isProcessing = false
    private var retryCount = 0
    private var onPermissionGranted: (() -> Unit)? = null
    private var onPermissionFailed: (() -> Unit)? = null

    // 状态管理
    private enum class PermissionPageState {
        UNKNOWN,                // 未知页面
        APP_INFO,              // 应用信息页面
        OVERLAY_PERMISSION     // 悬浮窗权限设置页面
    }

    private var currentPageState = PermissionPageState.UNKNOWN

    /**
     * 自动获取悬浮窗权限
     */
    fun autoGrantOverlayPermission(
        onGranted: () -> Unit,
        onFailed: () -> Unit
    ) {
        if (isProcessing) {
            XLog.w("权限获取正在进行中，忽略重复请求")
            return
        }

        // 检查是否已经有权限
        if (hasOverlayPermission()) {
            XLog.d("悬浮窗权限已存在")
            onGranted()
            return
        }

        XLog.d("开始自动获取悬浮窗权限")
        isProcessing = true
        retryCount = 0
        onPermissionGranted = onGranted
        onPermissionFailed = onFailed

        // 打开悬浮窗权限设置页面
        openOverlayPermissionSettings()
    }

    /**
     * 检查是否有悬浮窗权限
     */
    private fun hasOverlayPermission(): Boolean {
        return Settings.canDrawOverlays(accessibilityService)
    }

    /**
     * 打开应用信息页面
     */
    private fun openOverlayPermissionSettings() {
        try {
            // 直接打开应用信息页面，而不是悬浮窗权限页面
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = "package:${accessibilityService.packageName}".toUri()
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            accessibilityService.startActivity(intent)

            XLog.d("已打开应用信息页面")

            // 开始监控权限设置页面
            startMonitoringPermissionPage()

        } catch (e: Exception) {
            XLog.e("打开应用信息页面失败", e)
            onPermissionFailed?.invoke()
            resetState()
        }
    }

    /**
     * 开始监控权限设置页面
     */
    private fun startMonitoringPermissionPage() {
        coroutineScope.launch {
            delay(2000) // 等待页面加载

            while (isProcessing && retryCount < MAX_RETRY_COUNT) {
                try {
                    val rootNode = accessibilityService.rootInActiveWindow
                    if (rootNode != null) {
                        // 检测当前页面状态
                        val pageState = detectPageState(rootNode)
                        XLog.d("检测到页面状态: $pageState")

                        when (pageState) {
                            PermissionPageState.APP_INFO -> {
                                // 在应用信息页面，查找并点击悬浮窗权限项
                                if (tryClickOverlayPermissionItem(rootNode)) {
                                    XLog.d("成功点击悬浮窗权限项，等待进入权限设置页面")
                                    currentPageState = PermissionPageState.APP_INFO
                                    delay(1500) // 等待页面跳转
                                    continue
                                }
                            }

                            PermissionPageState.OVERLAY_PERMISSION -> {
                                // 在悬浮窗权限设置页面，尝试开启权限
                                if (tryEnableOverlayPermission(rootNode)) {
                                    XLog.d("成功点击开启悬浮窗权限")

                                    // 等待权限生效
                                    delay(1000)

                                    // 检查权限是否已获得
                                    if (hasOverlayPermission()) {
                                        XLog.d("悬浮窗权限获取成功，准备返回原来页面")

                                        Toaster.show("获取权限成功，请返回页面")

                                        // 等待页面切换完成后再调用回调
                                        delay(1500)

                                        onPermissionGranted?.invoke()
                                        resetState()
                                        return@launch
                                    }
                                }
                            }

                            PermissionPageState.UNKNOWN -> {
                                XLog.d("未识别的页面，继续等待")
                            }
                        }
                    }

                    retryCount++
                    delay(RETRY_DELAY_MS)

                } catch (e: Exception) {
                    XLog.e("监控权限设置页面时发生错误", e)
                    retryCount++
                    delay(RETRY_DELAY_MS)
                }
            }

            // 超时或失败
            XLog.w("自动获取悬浮窗权限超时或失败")
            onPermissionFailed?.invoke()
            resetState()
        }
    }

    /**
     * 检测当前页面状态
     */
    private fun detectPageState(rootNode: AccessibilityNodeInfo): PermissionPageState {
        try {
            // 检查包名
            val packageName = rootNode.packageName?.toString()
            if (packageName != SETTINGS_PACKAGE) {
                return PermissionPageState.UNKNOWN
            }


            // 检查是否为悬浮窗权限设置页面（包含开关控件和悬浮窗关键词）
            val overlayKeywords = listOf(
                "悬浮窗", "浮窗", "overlay", "在其他应用上层显示",
                "显示在其他应用的上层", "Draw over other apps",
                "Display over other apps", "Appear on top",
                "允许显示在其他应用的上层", "允许在其他应用上层显示"
            )
            val overlayNotKeywords = listOf(
                "通知", "存储", "权限"
            )

            val hasOverlayKeywords = findNodeWithText(rootNode, overlayKeywords) != null
            val notOverlayKeywords = findNodeWithText(rootNode, overlayNotKeywords) == null
            val hasSwitchControl = findSwitchNode(rootNode) != null

            if (hasOverlayKeywords && hasSwitchControl && notOverlayKeywords) {
                XLog.d("检测到悬浮窗权限设置页面")
                return PermissionPageState.OVERLAY_PERMISSION
            }


            // 检查是否为应用信息页面（包含应用信息相关关键词但没有开关控件）
            val appInfoKeywords = listOf(
                "应用信息", "App info", "应用详情", "App details",
                "权限", "Permissions", "存储", "Storage",
                "通知", "Notifications", "数据使用", "Data usage",
                "电池", "Battery", "内存", "Memory",
                "显示在其他应用的上层", "悬浮窗", "浮窗"
            )

            if (findNodeWithText(rootNode, appInfoKeywords) != null) {
                XLog.d("检测到应用信息页面")
                return PermissionPageState.APP_INFO
            }

            return PermissionPageState.UNKNOWN

        } catch (e: Exception) {
            XLog.e("检测页面状态时发生错误", e)
            return PermissionPageState.UNKNOWN
        }
    }

    /**
     * 检查是否有多个应用名称（判断是否为应用列表页面）
     */
    private fun hasMultipleAppNames(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 先检查是否为分屏布局，如果是则排除设置项目的干扰
            if (isSplitScreenLayout(rootNode)) {
                XLog.d("检测到分屏布局，使用特殊检测逻辑")
                return hasMultipleAppNamesInSplitScreen(rootNode)
            }

            // 收集所有可能的应用名称（排除设置项目）
            val commonAppNames = listOf(
                "相机", "Camera", "图库", "Gallery", "Photos",
                "微信", "WeChat", "QQ", "支付宝", "Alipay", "淘宝", "Taobao",
                "抖音", "TikTok", "快手", "Kuaishou", "微博", "Weibo",
                "Chrome", "Firefox", "Safari", "浏览器", "Browser",
                "音乐", "Music", "视频", "Video", "播放器", "Player",
                "地图", "Maps", "导航", "Navigation", "滴滴", "Didi",
                "美团", "Meituan", "饿了么", "Eleme", "京东", "JD",
                "Time Controller", getAppName() // 包含当前应用名
            )

            val foundAppNames = mutableSetOf<String>()

            // 查找页面中出现的应用名称
            for (appName in commonAppNames) {
                if (findNodeWithText(rootNode, listOf(appName)) != null) {
                    foundAppNames.add(appName)
                    XLog.d("找到应用名称: $appName")
                }
            }

            // 如果找到2个或以上的应用名称，认为是应用列表页面
            val isAppList = foundAppNames.size >= 2
            XLog.d("找到 ${foundAppNames.size} 个应用名称: $foundAppNames")
            XLog.d("判断为应用列表页面: $isAppList")

            return isAppList

        } catch (e: Exception) {
            XLog.e("检查多个应用名称时发生错误", e)
            return false
        }
    }

    /**
     * 检测是否为分屏布局
     */
    private fun isSplitScreenLayout(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 检查是否有设置项目的关键词（通常出现在左侧面板）
            val settingsKeywords = listOf(
                "应用管理", "App management", "应用信息", "App info",
                "权限管理", "Permission management", "隐私设置", "Privacy",
                "安全设置", "Security", "存储设置", "Storage",
                "网络设置", "Network", "显示设置", "Display",
                "声音设置", "Sound", "通知设置", "Notifications"
            )

            val hasSettingsItems = findNodeWithText(rootNode, settingsKeywords) != null

            // 检查是否同时有悬浮窗相关内容（通常出现在右侧面板）
            val overlayKeywords = listOf(
                "悬浮窗", "浮窗", "overlay", "在其他应用上层显示",
                "显示在其他应用的上层", "Draw over other apps",
                "Display over other apps", "Appear on top"
            )

            val hasOverlayContent = findNodeWithText(rootNode, overlayKeywords) != null

            val isSplitScreen = hasSettingsItems && hasOverlayContent
            XLog.d("分屏检测 - 设置项目: $hasSettingsItems, 悬浮窗内容: $hasOverlayContent, 分屏布局: $isSplitScreen")

            return isSplitScreen

        } catch (e: Exception) {
            XLog.e("检测分屏布局时发生错误", e)
            return false
        }
    }

    /**
     * 在分屏布局中检查是否有多个应用名称
     */
    private fun hasMultipleAppNamesInSplitScreen(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 在分屏模式下，重点查找右侧面板中的应用名称
            // 通过查找包含悬浮窗关键词的区域来定位右侧面板
            val rightPanel = findRightPanelInSplitScreen(rootNode)

            if (rightPanel != null) {
                XLog.d("找到右侧面板，在其中查找应用名称")
                return hasMultipleAppNamesInNode(rightPanel)
            } else {
                XLog.d("未找到右侧面板，使用全局检测")
                return hasMultipleAppNamesInNode(rootNode)
            }

        } catch (e: Exception) {
            XLog.e("在分屏布局中检查应用名称时发生错误", e)
            return false
        }
    }

    /**
     * 查找分屏布局中的右侧面板
     */
    private fun findRightPanelInSplitScreen(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            // 查找包含悬浮窗关键词的节点，这通常是右侧面板
            val overlayKeywords = listOf(
                "悬浮窗", "浮窗", "overlay", "在其他应用上层显示",
                "显示在其他应用的上层", "Draw over other apps",
                "Display over other apps", "Appear on top"
            )

            return findNodeWithText(rootNode, overlayKeywords)?.let { overlayNode ->
                // 向上查找包含该节点的较大容器
                findContainerNode(overlayNode)
            }

        } catch (e: Exception) {
            XLog.e("查找右侧面板时发生错误", e)
            return null
        }
    }

    /**
     * 查找包含指定节点的容器节点
     */
    private fun findContainerNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        var depth = 0
        val maxDepth = 3 // 限制查找深度

        while (parent != null && depth < maxDepth) {
            // 如果父节点有足够的子节点，可能是一个容器
            if (parent.childCount >= 2) {
                return parent
            }
            parent = parent.parent
            depth++
        }

        return node // 如果没找到合适的容器，返回原节点
    }

    /**
     * 在指定节点中检查是否有多个应用名称
     */
    private fun hasMultipleAppNamesInNode(node: AccessibilityNodeInfo): Boolean {
        try {
            val commonAppNames = listOf(
                "相机", "Camera", "图库", "Gallery", "Photos",
                "微信", "WeChat", "QQ", "支付宝", "Alipay", "淘宝", "Taobao",
                "抖音", "TikTok", "快手", "Kuaishou", "微博", "Weibo",
                "Chrome", "Firefox", "Safari", "浏览器", "Browser",
                "音乐", "Music", "视频", "Video", "播放器", "Player",
                "地图", "Maps", "导航", "Navigation", "滴滴", "Didi",
                "美团", "Meituan", "饿了么", "Eleme", "京东", "JD",
                "Time Controller", getAppName()
            )

            val foundAppNames = mutableSetOf<String>()

            for (appName in commonAppNames) {
                if (findNodeWithTextInSubtree(node, listOf(appName)) != null) {
                    foundAppNames.add(appName)
                    XLog.d("在指定区域找到应用名称: $appName")
                }
            }

            val isAppList = foundAppNames.size >= 2
            XLog.d("在指定区域找到 ${foundAppNames.size} 个应用名称: $foundAppNames")

            return isAppList

        } catch (e: Exception) {
            XLog.e("在指定节点中检查应用名称时发生错误", e)
            return false
        }
    }

    /**
     * 在子树中查找包含指定文本的节点
     */
    private fun findNodeWithTextInSubtree(
        rootNode: AccessibilityNodeInfo,
        keywords: List<String>
    ): AccessibilityNodeInfo? {
        try {
            val nodeText = rootNode.text?.toString()?.lowercase()
            val nodeContentDescription = rootNode.contentDescription?.toString()?.lowercase()

            for (keyword in keywords) {
                val lowerKeyword = keyword.lowercase()
                if ((nodeText?.contains(lowerKeyword) == true) ||
                    (nodeContentDescription?.contains(lowerKeyword) == true)
                ) {
                    return rootNode
                }
            }

            // 递归查找子节点
            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i) ?: continue
                val result = findNodeWithTextInSubtree(child, keywords)
                if (result != null) {
                    return result
                }
            }

        } catch (e: Exception) {
            XLog.e("在子树中查找文本节点时发生错误", e)
        }

        return null
    }

    /**
     * 检查是否有应用列表的结构特征
     */
    private fun hasAppListStructure(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 检查是否有列表控件
            val hasListView = findNodeByClassName(
                rootNode, listOf(
                    "RecyclerView", "ListView", "list"
                )
            ) != null

            // 检查是否有多个可点击的项目（通常应用列表中每个应用都是可点击的）
            val clickableItems = countClickableItems(rootNode)
            val hasMultipleClickableItems = clickableItems >= 3 // 至少3个可点击项目

            // 检查页面标题是否包含列表相关的关键词
            val listTitleKeywords = listOf(
                "应用列表", "应用管理", "应用权限", "App list", "App permissions",
                "管理应用", "Manage apps", "应用信息", "App info",
                "悬浮窗权限", "Overlay permission", "显示在其他应用上层"
            )
            val hasListTitle = findNodeWithText(rootNode, listTitleKeywords) != null

            val isAppListStructure = hasListView || hasMultipleClickableItems || hasListTitle

            XLog.d("列表结构检查 - 列表控件: $hasListView, 可点击项目: $clickableItems, 列表标题: $hasListTitle")
            XLog.d("判断为应用列表结构: $isAppListStructure")

            return isAppListStructure

        } catch (e: Exception) {
            XLog.e("检查应用列表结构时发生错误", e)
            return false
        }
    }

    /**
     * 统计可点击项目的数量
     */
    private fun countClickableItems(rootNode: AccessibilityNodeInfo): Int {
        try {
            var count = 0

            if (rootNode.isClickable) {
                count++
            }

            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i) ?: continue
                count += countClickableItems(child)
            }

            return count

        } catch (e: Exception) {
            XLog.e("统计可点击项目时发生错误", e)
            return 0
        }
    }

    /**
     * 获取当前应用名称
     */
    private fun getAppName(): String {
        return try {
            val packageManager = accessibilityService.packageManager
            val applicationInfo =
                packageManager.getApplicationInfo(accessibilityService.packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: Exception) {
            XLog.e("获取应用名称失败", e)
            "Time Controller" // 默认应用名
        }
    }

    /**
     * 尝试点击当前应用的item
     */
    private fun tryClickCurrentApp(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            val appName = getAppName()
            XLog.d("查找应用: $appName")

            // 查找包含应用名称的节点
            val appNode = findNodeWithText(rootNode, listOf(appName))
            if (appNode != null) {
                // 查找可点击的父节点
                val clickableNode = findClickableParent(appNode)
                if (clickableNode != null && clickableNode.isClickable) {
                    XLog.d("找到可点击的应用节点，尝试点击")
                    return clickableNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }

                // 如果节点本身可点击
                if (appNode.isClickable) {
                    XLog.d("应用节点本身可点击，尝试点击")
                    return appNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }
            }

            XLog.w("未找到可点击的应用节点: $appName")
            return false

        } catch (e: Exception) {
            XLog.e("点击当前应用时发生错误", e)
            return false
        }
    }

    /**
     * 尝试点击悬浮窗权限项
     */
    private fun tryClickOverlayPermissionItem(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 查找悬浮窗权限相关的文本
            val overlayKeywords = listOf(
                "显示在其他应用的上层", "悬浮窗", "浮窗", "overlay",
                "在其他应用上层显示", "Draw over other apps",
                "Display over other apps", "Appear on top",
                "允许显示在其他应用的上层", "允许在其他应用上层显示"
            )

            XLog.d("查找悬浮窗权限项")

            // 查找包含悬浮窗关键词的节点
            val overlayNode = findNodeWithText(rootNode, overlayKeywords)
            if (overlayNode != null) {
                XLog.d("找到悬浮窗权限项文本节点")


                // 如果节点本身可点击
                if (overlayNode.isClickable) {
                    XLog.d("悬浮窗权限项节点本身可点击，尝试点击")
                    return overlayNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }

                // 查找可点击的父节点
                val clickableNode = findClickableParent(overlayNode)
                if (clickableNode != null && clickableNode.isClickable) {
                    XLog.d("找到可点击的悬浮窗权限项，尝试点击")
                    return clickableNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }

                // 尝试查找同级或附近的可点击节点
                val nearbyClickableNode = findNearbyClickableNode(overlayNode)
                if (nearbyClickableNode != null) {
                    XLog.d("找到附近的可点击节点，尝试点击")
                    return nearbyClickableNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }
            }

            XLog.w("未找到可点击的悬浮窗权限项")
            return false

        } catch (e: Exception) {
            XLog.e("点击悬浮窗权限项时发生错误", e)
            return false
        }
    }

    /**
     * 查找附近的可点击节点
     */
    private fun findNearbyClickableNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            // 先检查父节点的其他子节点
            val parent = node.parent
            if (parent != null) {
                for (i in 0 until parent.childCount) {
                    val sibling = parent.getChild(i) ?: continue
                    if (sibling.isClickable) {
                        return sibling
                    }
                    // 递归检查子节点
                    val clickableChild = findClickableNodeInSubtree(sibling)
                    if (clickableChild != null) {
                        return clickableChild
                    }
                }
            }
        } catch (e: Exception) {
            XLog.e("查找附近可点击节点时发生错误", e)
        }
        return null
    }

    /**
     * 在子树中查找可点击节点
     */
    private fun findClickableNodeInSubtree(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            if (node.isClickable) {
                return node
            }

            for (i in 0 until node.childCount) {
                val child = node.getChild(i) ?: continue
                val result = findClickableNodeInSubtree(child)
                if (result != null) {
                    return result
                }
            }
        } catch (e: Exception) {
            XLog.e("在子树中查找可点击节点时发生错误", e)
        }
        return null
    }

    /**
     * 查找可点击的父节点
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        var depth = 0
        val maxDepth = 5 // 限制查找深度

        while (parent != null && depth < maxDepth) {
            if (parent.isClickable) {
                return parent
            }
            parent = parent.parent
            depth++
        }

        return null
    }

    /**
     * 尝试自动开启悬浮窗权限
     */
    private fun tryEnableOverlayPermission(rootNode: AccessibilityNodeInfo): Boolean {
        try {
            // 查找开关控件
            val switchNode = findSwitchNode(rootNode)
            if (switchNode != null && !switchNode.isChecked) {
                XLog.d("找到未开启的悬浮窗权限开关，尝试点击")
                if (switchNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
                    return true
                } else {
                    return clickSwitchParent(switchNode)
                }

            }

            // 查找允许按钮
            val allowButton = findNodeWithText(
                rootNode, listOf(
                    "允许", "开启", "启用", "Allow", "Enable", "Turn on", "确定", "OK"
                )
            )
            if (allowButton != null && allowButton.isClickable) {
                XLog.d("找到允许按钮，尝试点击")
                return allowButton.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            }

            XLog.w("未找到可操作的悬浮窗权限控件")
            return false

        } catch (e: Exception) {
            XLog.e("尝试开启悬浮窗权限时发生错误", e)
            return false
        }
    }

    fun clickSwitchParent(switchNode: AccessibilityNodeInfo?): Boolean {
        var node = switchNode
        for (i in 0 until 4) { // 向上找4层内的父节点
            node = node?.parent
            if (node?.isClickable == true) {
                return node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            }
        }
        return false
    }

    /**
     * 查找开关节点
     */
    private fun findSwitchNode(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            // 查找Switch或ToggleButton
            val switchNodes =
                rootNode.findAccessibilityNodeInfosByViewId("android:id/switch_widget")
            if (switchNodes.isNotEmpty()) {
                return switchNodes[0]
            }

            // 递归查找
            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i) ?: continue
                val className = child.className?.toString()

                if (className == "android.widget.Switch" ||
                    className == "android.widget.ToggleButton" ||
                    className == "androidx.appcompat.widget.SwitchCompat"
                ) {
                    return child
                }

                val result = findSwitchNode(child)
                if (result != null) {
                    return result
                }
            }

        } catch (e: Exception) {
            XLog.e("查找开关节点时发生错误", e)
        }

        return null
    }

    /**
     * 查找包含指定文本的节点
     */
    private fun findNodeWithText(
        rootNode: AccessibilityNodeInfo,
        keywords: List<String>
    ): AccessibilityNodeInfo? {
        try {
            val nodeText = rootNode.text?.toString()?.lowercase()
            val nodeContentDescription = rootNode.contentDescription?.toString()?.lowercase()

            XLog.d("搜索：$nodeContentDescription")
            for (keyword in keywords) {
                val lowerKeyword = keyword.lowercase()
                if ((nodeText?.contains(lowerKeyword) == true) ||
                    (nodeContentDescription?.contains(lowerKeyword) == true)
                ) {
                    return rootNode
                }
            }

            // 递归查找子节点
            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i) ?: continue
                val result = findNodeWithText(child, keywords)
                if (result != null) {
                    return result
                }
            }

        } catch (e: Exception) {
            XLog.e("查找文本节点时发生错误", e)
        }

        return null
    }

    /**
     * 根据类名查找节点
     */
    private fun findNodeByClassName(
        rootNode: AccessibilityNodeInfo,
        classNames: List<String>
    ): AccessibilityNodeInfo? {
        try {
            val nodeClassName = rootNode.className?.toString()?.lowercase()

            for (className in classNames) {
                val lowerClassName = className.lowercase()
                if (nodeClassName?.contains(lowerClassName) == true) {
                    return rootNode
                }
            }

            // 递归查找子节点
            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i) ?: continue
                val result = findNodeByClassName(child, classNames)
                if (result != null) {
                    return result
                }
            }

        } catch (e: Exception) {
            XLog.e("根据类名查找节点时发生错误", e)
        }

        return null
    }

    /**
     * 重置状态
     */
    private fun resetState() {
        isProcessing = false
        retryCount = 0
        currentPageState = PermissionPageState.UNKNOWN
        onPermissionGranted = null
        onPermissionFailed = null
    }

    /**
     * 处理无障碍事件（由无障碍服务调用）
     */
    fun onAccessibilityEvent(event: AccessibilityEvent) {
        if (!isProcessing) return

        // 监听窗口状态变化，用于检测权限页面
        if (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            val packageName = event.packageName?.toString()
            XLog.d("窗口状态变化: $packageName")

            // 如果返回到我们的应用，检查权限是否已获得
            if (packageName == accessibilityService.packageName) {
                handler.postDelayed({
                    if (hasOverlayPermission()) {
                        XLog.d("检测到悬浮窗权限已获得")
                        onPermissionGranted?.invoke()
                        resetState()
                    }
                }, 500)
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        resetState()
        coroutineScope.coroutineContext[Job]?.cancel()
    }
}
