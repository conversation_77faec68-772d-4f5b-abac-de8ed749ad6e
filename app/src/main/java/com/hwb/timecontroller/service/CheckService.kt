package com.hwb.timecontroller.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.hwb.timecontroller.R
import com.hwb.timecontroller.activity.LoginActivity
import com.hwb.timecontroller.business.AppDataCleanupManager
import com.hwb.timecontroller.business.CountdownManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.service.keepalive.KeepAliveCapable
import com.hwb.timecontroller.service.keepalive.MutualKeepAliveManager
import com.elvishew.xlog.XLog

/**
 * 应用启动权限验证服务
 * 提供AIDL接口供第三方应用调用，替代CheckWhenLauncherActivity
 *
 * Author: huangwubin
 * Contacts: <EMAIL>
 *
 * changeLogs:
 * 2025/7/7: First created this service for AIDL implementation.
 */
class CheckService : Service(), KeepAliveCapable {

    companion object {
        private const val NOTIFICATION_ID = 1003
        private const val CHANNEL_ID = "check_service_channel"
        private const val SERVICE_VERSION = "1.0.0"

        /**
         * 启动CheckService
         */
        fun start(context: Context) {
            try {
                val intent = Intent(context, CheckService::class.java)
                context.startForegroundService(intent)
                XLog.d("CheckService启动请求已发送")
            } catch (e: Exception) {
                XLog.e("启动CheckService失败", e)
            }
        }

        /**
         * 停止CheckService
         */
        fun stop(context: Context) {
            try {
                val intent = Intent(context, CheckService::class.java)
                context.stopService(intent)
                XLog.d("CheckService停止请求已发送")
            } catch (e: Exception) {
                XLog.e("停止CheckService失败", e)
            }
        }
    }

    // 保活管理器
    private lateinit var keepAliveManager: MutualKeepAliveManager

    override fun onCreate() {
        super.onCreate()
        XLog.d("CheckService创建")

        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = CheckService::class.java,
            serviceDisplayName = "应用验证服务"
        )

        // 注册需要保活的伙伴服务
        keepAliveManager.registerPartnerServices(
            CountdownService::class.java,
            FloatingWindowService::class.java,
            AppLifecycleManagerService::class.java,
            AppLifecycleAccessibilityService::class.java
        )

        // 创建通知渠道
        createNotificationChannel()

        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification())

        XLog.d("CheckService初始化完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        XLog.d("CheckService启动命令")

        // 启动保活管理
        keepAliveManager.startKeepAlive()

        return START_STICKY // 服务被杀死后自动重启
    }

    override fun onBind(intent: Intent?): IBinder {
        XLog.d("CheckService绑定")
        return checkServiceBinder
    }

    override fun onDestroy() {
        super.onDestroy()
        XLog.d("CheckService销毁")

        // 停止保活管理
        keepAliveManager.stopKeepAlive()
    }

    /**
     * AIDL服务实现
     * 注意：ICheckService会在编译时由AIDL文件自动生成
     */

    private val checkServiceBinder = object : ICheckService.Stub() {

        override fun check(need_show_lock: Boolean): Boolean {
            // 获取调用方包名并记录为清理目标
            val callingPackageName = getCallingPackageName()
            if (callingPackageName != null) {
                AppDataCleanupManager.recordCleanupTargetApp(callingPackageName)
            }
            XLog.d("开始检查能否启动应用（${callingPackageName}），need_show_lock: $need_show_lock")
            return performCheck(need_show_lock)
        }
    }


    /**
     * 执行权限检查
     * @param need_show_lock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    private fun performCheck(need_show_lock: Boolean): Boolean {
        try {
            XLog.d("执行权限检查, need_show_lock: $need_show_lock")

            // 1. 检查倒计时状态
            val isCountdownRunning =
                UserManager.isUserLoggedIn() &&
                         CountdownManager.isCountdownRunning()
            if (isCountdownRunning) {
                XLog.d("倒计时正在运行，允许启动")
                return true
            }

            // 2. 如果需要显示锁定界面，则启动LockActivity
            if (need_show_lock) {
                XLog.d("需要显示锁定界面，启动LockActivity")
                launchLockActivity()
                return false // 返回false，表示需要用户验证
            }

            // 3. 不需要显示锁定界面，直接返回false
            XLog.d("不需要显示锁定界面，直接拒绝")
            return false

        } catch (e: Exception) {
            XLog.e("执行权限检查时发生错误", e)
            return false
        }
    }

    /**
     * 启动LockActivity进行用户验证
     */
    private fun launchLockActivity() {
        try {
            val intent = Intent(this, LoginActivity::class.java).apply {
                putExtra(LoginActivity.EXTRA_SHOW_LOGIN_UI, true)
                // 从Service启动Activity需要NEW_TASK标志
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }

            startActivity(intent)
            XLog.d("启动LockActivity进行用户验证")

        } catch (e: Exception) {
            XLog.e("启动LockActivity失败", e)
        }
    }

    /**
     * 获取调用方包名
     * @return 调用方包名，如果获取失败返回null
     */
    private fun getCallingPackageName(): String? {
        return try {
            val callingUid = android.os.Binder.getCallingUid()
            val packageManager = packageManager
            val packages = packageManager.getPackagesForUid(callingUid)

            if (packages != null && packages.isNotEmpty()) {
                val packageName = packages[0]
                XLog.d("检测到应用调用CheckService: $packageName (UID: $callingUid)")
                packageName
            } else {
                XLog.w("无法获取调用方包名，UID: $callingUid")
                null
            }
        } catch (e: Exception) {
            XLog.e("获取调用方包名失败", e)
            null
        }
    }


    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "应用验证服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "应用启动权限验证服务"
            setShowBadge(false)
        }

        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("应用验证服务")
            .setContentText("正在监控应用启动权限")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setShowWhen(false)
            .build()
    }

    // KeepAliveCapable接口实现
    override fun getKeepAliveManager(): MutualKeepAliveManager = keepAliveManager

    override fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        XLog.w("伙伴服务死亡: ${serviceClass.simpleName}")
        // 可以在这里添加特定的恢复逻辑
    }
}
