package com.hwb.timecontroller.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Base64
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import coil.load
import coil.transform.CircleCropTransformation
import com.elvishew.xlog.XLog
import com.hi.dhl.binding.viewbind
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.BalancePollingManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.databinding.ActivityUserInfoBinding
import com.hwb.timecontroller.service.CountdownService
import com.hwb.timecontroller.viewModel.LoginViewModel
import com.hwb.timecontroller.viewModel.UserInfoViewModel
import com.kongzue.dialogx.dialogs.MessageDialog

/**
 * 用户信息页面
 *
 * Author: huangwubin
 * Date: 2025/7/11
 */
class UserInfoActivity : AppCompatActivity() {

    companion object {

        /**
         * 启动用户信息页面
         * @param context 上下文
         */
        fun start(context: Context) {
            val intent = Intent(context, UserInfoActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            context.startActivity(intent)
        }
    }

    private val binding by viewbind<ActivityUserInfoBinding>()
    private val viewModel: UserInfoViewModel by viewModels()
    private val loginViewModel by viewModels<LoginViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(binding.root)

        // 设置全屏
        setupFullscreen()
        supportActionBar?.hide()

        // 获取传入的用户数据
        extractUserData()

        // 初始化视图
        initViews()
        setupClickListeners()
        setupObservers()

        // 暂停倒计时和扣款
        pauseCountdownAndDeduction()

        // 主动刷新余额
        viewModel.refreshBalanceOnEnter()

        binding.ivQrCode.post {
            //生成二维码
            loginViewModel.generateQRCodeDirectly()

        }
    }

    /**
     * 设置全屏模式
     */
    private fun setupFullscreen() {
        // Android 11+ 使用新的WindowInsetsController API
        window.insetsController?.let { controller ->
            controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
            controller.systemBarsBehavior =
                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }

    /**
     * 提取用户数据
     */
    private fun extractUserData() {
        val userInfo = UserManager.getClientLoginData()
        if (userInfo == null) {
            XLog.w("未获取到用户数据")
            // 如果没有用户数据，直接返回
            finish()
        } else {
            viewModel.setUserData(userInfo)
            XLog.d("获取到用户数据: ${userInfo.userName}")
        }
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        // 初始化时更新UI
        updateUserInfo()
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 返回按钮（已隐藏，但保留逻辑）
        binding.btnBack.setOnClickListener {
            // 不执行任何操作，屏蔽返回功能
        }

        // 开始游玩按钮
        binding.btnStartPlaying.setOnClickListener {
            handleStartPlayingClick()
        }

        // 退出登录按钮
        binding.btnLogout.setOnClickListener {
            showLogoutConfirmDialog()
        }

        // 刷新余额按钮
        binding.ivRefreshBalance.setOnClickListener {
            viewModel.refreshBalance()
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察用户数据变化
        viewModel.userData.observe(this, Observer { userData ->
            updateUserInfo()
        })

        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            // 可以在这里显示/隐藏加载指示器
            binding.btnLogout.isEnabled = !isLoading
        })

        // 观察错误信息
        viewModel.errorMessage.observe(this, Observer { error ->
            if (!error.isNullOrEmpty()) {
                MessageDialog.show("错误", error)
                viewModel.clearError()
            }
        })

        // 观察退出登录结果
        viewModel.logoutResult.observe(this, Observer { success ->
            if (success) {
                XLog.d("退出登录成功，关闭页面")
                finish()
            }
        })

        // 观察余额刷新状态
        viewModel.isRefreshingBalance.observe(this, Observer { isRefreshing ->
            // 更新刷新按钮状态
            binding.ivRefreshBalance.isEnabled = !isRefreshing
            binding.ivRefreshBalance.alpha = if (isRefreshing) 0.5f else 1.0f

            // 可以添加旋转动画
            if (isRefreshing) {
                binding.ivRefreshBalance.animate()
                    .rotation(360f)
                    .setDuration(1000)
                    .withEndAction {
                        binding.ivRefreshBalance.rotation = 0f
                    }
            }
        })

        loginViewModel.qrCodeBase64.observe(this, Observer { base64 ->
            if (!base64.isNullOrEmpty()) {
                loadQRCodeFromBase64(base64)
            }
        })

        // 观察开始游玩结果
        viewModel.startPlayingResult.observe(this, Observer { success ->
            if (success) {
                XLog.d("开始游玩成功，关闭用户信息页面")
                finish()
            }
        })
    }

    /**
     * 从Base64数据加载小程序码图片
     */
    private fun loadQRCodeFromBase64(base64: String) {
        try {
            // 解码Base64数据
            val imageBytes = Base64.decode(base64, Base64.DEFAULT)

            // 使用Coil加载图片
            binding.ivQrCode.load(imageBytes) {
                crossfade(true)
                placeholder(R.drawable.ic_launcher_foreground)
                error(R.drawable.ic_launcher_foreground)
            }

            XLog.d("加载小程序码成功，数据长度: ${imageBytes.size}")
        } catch (e: Exception) {
            XLog.e("加载小程序码失败", e)
        }
    }


    /**
     * 更新用户信息显示
     */
    private fun updateUserInfo() {
        val userData = viewModel.userData.value
        if (userData != null) {
            // 设置用户昵称
            binding.tvUserName.text = viewModel.getUserDisplayName()

            // 设置用户余额
            binding.tvUserBalance.text = viewModel.getUserBalanceText()

            // 加载用户头像
            loadUserAvatar(viewModel.getUserAvatarUrl())
        }
    }

    /**
     * 加载用户头像
     * @param avatarUrl 头像URL
     */
    private fun loadUserAvatar(avatarUrl: String?) {
        try {
            // 如果没有头像URL，直接显示默认头像
            if (avatarUrl.isNullOrBlank()) {
                binding.ivUserAvatar.setImageResource(R.drawable.ic_default_avatar)
                binding.pbAvatarLoading.visibility = View.GONE
                return
            }

            binding.ivUserAvatar.load(avatarUrl) {
                crossfade(true)
                placeholder(R.drawable.ic_default_avatar)
                error(R.drawable.ic_default_avatar)
                transformations(CircleCropTransformation())
                listener(
                    onStart = {
                        XLog.d("开始加载用户头像: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.VISIBLE
                    },
                    onError = { _, throwable ->
                        XLog.w("加载用户头像失败: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.GONE
                    },
                    onSuccess = { _, _ ->
                        XLog.d("用户头像加载成功: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.GONE
                    }
                )
            }
        } catch (e: Exception) {
            XLog.e("设置用户头像失败", e)
            // 如果出现异常，设置默认头像并隐藏加载指示器
            binding.ivUserAvatar.setImageResource(R.drawable.ic_default_avatar)
            binding.pbAvatarLoading.visibility = View.GONE
        }
    }

    /**
     * 显示退出登录确认对话框
     */
    private fun showLogoutConfirmDialog() {
        MessageDialog.show("退出登录", "确定要退出登录吗？退出后将清理所有应用数据")
            .setOkButton("确定") { _, _ ->
                // 直接启动AppDataCleanupActivity进行清理
                val intent = Intent(this@UserInfoActivity, AppDataCleanupActivity::class.java)
                startActivity(intent)
                finish()
                false
            }
            .setCancelButton("取消")
    }

    /**
     * 处理开始游玩按钮点击
     */
    private fun handleStartPlayingClick() {
        try {
            XLog.d("开始游玩按钮点击，先刷新余额和倒计时")
            // 先刷新余额和倒计时，然后检查是否足够
            viewModel.startPlayingWithRefresh()
        } catch (e: Exception) {
            XLog.e("开始游玩时发生错误", e)
            MessageDialog.show("错误", "开始游玩时发生错误，请稍后重试")
                .setOkButton("确定")
        }
    }

    /**
     * 屏蔽返回键
     */
    override fun onBackPressed() {
        // 不执行任何操作，屏蔽返回键功能
        XLog.d("返回键被屏蔽")
    }

    /**
     * 暂停倒计时和扣款
     */
    private fun pauseCountdownAndDeduction() {
        try {
            CountdownService.pauseCountdownStatic()
            XLog.d("用户信息页面：暂停倒计时和扣款")
        } catch (e: Exception) {
            XLog.e("暂停倒计时失败", e)
        }
    }

    /**
     * 恢复倒计时和扣款
     */
    private fun resumeCountdownAndDeduction() {
        try {
            com.hwb.timecontroller.service.CountdownService.resumeCountdownStatic()
            XLog.d("用户信息页面：恢复倒计时和扣款")
        } catch (e: Exception) {
            XLog.e("恢复倒计时失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 恢复倒计时和扣款
        resumeCountdownAndDeduction()
        XLog.d("用户信息页面销毁，恢复倒计时")
    }
}
